<view class="container">
  <!-- 主页内容区域 -->
  <view class="main-content">
    <!-- AI问卦功能区 -->
    <view class="gua-section">
      <view class="gua-title">AI问卦</view>

      <!-- 使用次数显示 -->
      <view class="usage-info">
        <text class="usage-text">今日已使用：{{guaUsageInfo.current}}次</text>
        <text class="usage-text">剩余次数：{{guaUsageInfo.remaining}}次</text>
      </view>

      <!-- AI问卦按钮 -->
      <button
        class="gua-button {{guaButtonDisabled ? 'disabled' : ''}}"
        bindtap="showGuaModal"
        disabled="{{guaButtonDisabled}}"
      >
        {{guaButtonDisabled ? 'AI问卦(' + guaButtonCooldown + 's)' : 'AI问卦'}}
      </button>
    </view>

    <!-- 历史记录区 -->
    <view class="history-section">
      <view class="history-title">问卦历史</view>
      <view class="history-list">
        <block wx:if="{{guaHistory.length > 0}}">
          <view class="history-item" wx:for="{{guaHistory}}" wx:key="id" bindtap="viewGuaHistoryDetail" data-id="{{item.id}}">
            <view class="history-question">{{item.question}}</view>
            <view class="history-hexagram">{{item.result.hexagram.symbol}} {{item.result.hexagram.name}}</view>
            <view class="history-time">{{item.time}}</view>
          </view>
        </block>
        <view class="empty-tip" wx:else>
          <text>暂无问卦记录</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 授权弹窗 -->
  <view class="auth-popup" wx:if="{{showAuthDialog}}">
    <view class="auth-mask" bindtap="closeAuthDialog"></view>
    <view class="auth-dialog">
      <view class="auth-title">微信授权登录</view>
      <view class="auth-content">
        <text>请授权获取您的微信头像和昵称</text>
        <text class="auth-tips">授权后将获取您的IP地址用于安全验证</text>
      </view>
      <view class="auth-buttons">
        <button class="auth-cancel" bindtap="closeAuthDialog">取消</button>
        <button class="auth-confirm" type="primary" open-type="getUserInfo" bindtap="onGetUserProfile">确认授权</button>
      </view>
    </view>
  </view>
</view>
